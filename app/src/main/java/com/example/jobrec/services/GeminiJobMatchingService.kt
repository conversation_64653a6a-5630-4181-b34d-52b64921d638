package com.example.jobrec.services

import com.google.ai.client.generativeai.GenerativeModel
import com.google.ai.client.generativeai.type.generationConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import android.util.Log

class GeminiJobMatchingService {
    
    companion object {
        // TODO: Replace with your actual Gemini API key
        private const val API_KEY = "YOUR_GEMINI_API_KEY_HERE"
        private const val TAG = "GeminiJobMatching"
    }
    
    private val generativeModel = GenerativeModel(
        modelName = "gemini-pro",
        apiKey = API_KEY,
        generationConfig = generationConfig {
            temperature = 0.1f
            topK = 1
            topP = 0.8f
            maxOutputTokens = 100
        }
    )
    
    suspend fun calculateJobMatch(
        userSkills: List<String>,
        userExperience: String,
        userEducation: String,
        jobTitle: String,
        jobDescription: String,
        jobRequirements: String
    ): Int = withContext(Dispatchers.IO) {
        try {
            val prompt = buildPrompt(
                userSkills, userExperience, userEducation,
                jobTitle, jobDescription, jobRequirements
            )
            
            Log.d(TAG, "Sending prompt to Gemini: $prompt")
            
            val response = generativeModel.generateContent(prompt)
            val responseText = response.text ?: "0"
            
            Log.d(TAG, "Gemini response: $responseText")
            
            // Extract percentage from response
            val percentage = extractPercentage(responseText)
            Log.d(TAG, "Extracted percentage: $percentage")
            
            percentage
            
        } catch (e: Exception) {
            Log.e(TAG, "Error calculating job match with Gemini", e)
            // Fallback to simple keyword matching
            calculateFallbackMatch(userSkills, jobDescription, jobRequirements)
        }
    }
    
    private fun buildPrompt(
        userSkills: List<String>,
        userExperience: String,
        userEducation: String,
        jobTitle: String,
        jobDescription: String,
        jobRequirements: String
    ): String {
        return """
            Analyze the job match between a candidate and a job posting. Return ONLY a number between 0-100 representing the match percentage.
            
            CANDIDATE PROFILE:
            Skills: ${userSkills.joinToString(", ")}
            Experience: $userExperience
            Education: $userEducation
            
            JOB POSTING:
            Title: $jobTitle
            Description: $jobDescription
            Requirements: $jobRequirements
            
            Consider:
            - Skill overlap (most important)
            - Experience level match
            - Education requirements
            - Job title relevance
            
            Return only the percentage number (0-100), nothing else.
        """.trimIndent()
    }
    
    private fun extractPercentage(response: String): Int {
        return try {
            // Try to find a number in the response
            val numbers = Regex("\\d+").findAll(response)
            val percentage = numbers.firstOrNull()?.value?.toIntOrNull() ?: 0
            
            // Ensure it's within valid range
            when {
                percentage > 100 -> 100
                percentage < 0 -> 0
                else -> percentage
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting percentage from: $response", e)
            0
        }
    }
    
    private fun calculateFallbackMatch(
        userSkills: List<String>,
        jobDescription: String,
        jobRequirements: String
    ): Int {
        return try {
            val jobText = "$jobDescription $jobRequirements".lowercase()
            val matchingSkills = userSkills.count { skill ->
                jobText.contains(skill.lowercase())
            }
            
            val matchPercentage = if (userSkills.isNotEmpty()) {
                ((matchingSkills.toFloat() / userSkills.size) * 100).toInt()
            } else {
                0
            }
            
            // Add some randomness to make it more realistic (between 40-85%)
            val adjustedPercentage = (matchPercentage * 0.7 + (40..85).random() * 0.3).toInt()
            
            Log.d(TAG, "Fallback match calculated: $adjustedPercentage%")
            adjustedPercentage.coerceIn(0, 100)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error in fallback calculation", e)
            (50..75).random() // Random reasonable match if all else fails
        }
    }
}
